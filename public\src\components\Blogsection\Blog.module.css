@import url("https://fonts.googleapis.com/css2?family=Inter:wght@700&display=swap");

.blog-main {
  max-width: 1100px;
  width: 100%;
  margin: 1rem auto 2rem auto;
  display: flex;
  flex-flow: nowrap column;
  gap: 10px;
}

.wide-post {
  width: 100%;
  display: flex;
  flex-flow: wrap row;
  gap: 40px;
  margin-bottom: 1rem;
}

.img {
  flex: 1 1 25%;
  object-fit: cover;
  height: 100%;
  position: relative;
}

.img .img-img {
  width: 100%;
  height: 300px;
  border-radius: 25px;
}

.arrow-main,
.arrow-main-snd {
  border: 1px solid black;
  position: absolute;
  top: 0px;
  width: 100%;
  height: 300px;
  justify-content: center;
  align-items: center;
  border-radius: 25px;
  background-color: rgba(0, 0, 0, 0.363);
  border: 0px;
  display: none;
}

.arrow,
.arrow-snd {
  position: absolute;
  top: 40%;
  width: 40px;
  height: 40px;
  background-color: #22a6fe;
  border-radius: 50%;
  border: 0;
  transform: rotate(320deg);
}

.img:hover .arrow-main {
  display: flex;
}

.wide-text-section {
  flex: 1 1 40%;
  display: flex;
  flex-flow: nowrap column;
}

.cat-name {
  color: #22a6fe;
  font-weight: 500;
  padding-bottom: 1rem;
}

.wide-text-section h2 {
  color: #0a2557;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
  font-family: "Inter", sans-serif;
}

.desc {
  color: #5a5666;
  padding-top: 1rem;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.name-date {
  display: flex;
  flex-flow: nowrap row;
  align-items: center;
  padding-top: 1rem;
}

.name {
  color: #042152;
  font-size: 0.8rem;
  padding-right: 2rem;
}

.date {
  color: #5a5666;
  font-size: 0.8rem;
}

.small-post {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 35px;
  margin-bottom: 5px;
  justify-content: center;
}

.posts {
  width: 100%;
  word-wrap: unset;
  text-overflow: ellipsis;
  display: flex;
  flex-flow: nowrap column;
  gap: 8px;
  position: relative;
}

.posts .small-img {
  width: 100%;
  height: 220px;
  border-radius: 25px;
}

.cat {
  font-weight: 500;
  color: #22a6fe;
}

.small-post h3 {
  font-size: 18px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  color: #0a2557;
  font-family: "Inter", sans-serif;
}

.desc-two {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-bottom: 24px;
  font-size: 0.9rem;
  color: #535257;
}

.name-date-snd {
  position: absolute;
  bottom: 0px;
}

.arrow-main-snd {
  height: 220px;
  display: none;
}

.arrow-snd {
  width: 35px;
  height: 35px;
}

.posts:hover .arrow-main-snd {
  display: flex;
}

.no-post {
  width: 100%;
  min-height: 70dvh;
  text-align: center;
}

.no-post p {
  max-width: 1100px;
  width: 100%;
  margin: auto;
  color: white;
  font-weight: 700;
  border-radius: 25px;
  margin-top: 2rem;
  font-size: 1.2rem;
  background-color: #7648cc;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%25' height='100%25' viewBox='0 0 800 800'%3E%3Cg %3E%3Ccircle fill='%237648CC' cx='400' cy='400' r='600'/%3E%3Ccircle fill='%236e41bb' cx='400' cy='400' r='500'/%3E%3Ccircle fill='%23653aab' cx='400' cy='400' r='400'/%3E%3Ccircle fill='%235d339a' cx='400' cy='400' r='300'/%3E%3Ccircle fill='%23542c8a' cx='400' cy='400' r='200'/%3E%3Ccircle fill='%234C2579' cx='400' cy='400' r='100'/%3E%3C/g%3E%3C/svg%3E");
  background-attachment: fixed;
  background-size: cover;
  padding: 20px 30px;
}

.loader {
  max-width: 1100px;
  width: 100%;
  margin: 2rem auto 3rem;
  height: 40vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

@media (max-width: 1300px) {
  .img {
    flex: 1 1 35%;
  }

  .wide-text-section {
    flex: 1 1 40%;
  }
}

@media (max-width: 1100px) {
  .blog-main {
    padding: 1rem;
  }
  .no-post {
    padding: 1rem;
  }
}

@media (max-width: 981px) {
  .img {
    flex: 1 1 40%;
  }

  .wide-text-section {
    flex: 1 1 40%;
  }
  .wide-post {
    gap: 15px;
  }
}

@media (max-width: 880px) {
  .img {
    flex-basis: 40%;
  }

  .img .img-img,
  .arrow-main {
    height: 280px;
  }

  .wide-text-section {
    flex-basis: 30%;
  }
  .wide-post {
    gap: 10px;
  }
}

@media (max-width: 800px) {
  .bloges img {
    height: 250px;
  }
  .img {
    flex-basis: 40%;
  }

  .img .img-img,
  .arrow-main {
    height: 280px;
  }
  .wide-text-section {
    padding: 0px 0.2rem;
  }
  .cat-name {
    padding-bottom: 0.4rem;
  }

  .wide-text-section h2 {
    font-size: 18px;
  }
  .desc {
    font-size: 0.9rem;
    padding-top: 0.4rem;
  }

  .small-post {
    grid-template-columns: repeat(auto-fill, minmax(270px, 1fr));
    gap: 25px;
  }

  .cat,
  .title-two,
  .desc-two,
  .name-date-snd {
    padding: 0 0.2rem;
  }
}

@media (max-width: 726px) {
  .img .img-img,
  .arrow-main {
    height: 250px;
  }

  .no-post {
    padding: 8px;
  }
}

@media (max-width: 580px) {
  .img {
    flex-basis: 100%;
  }
  .small-post {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100%, 1fr));
    gap: 25px;
  }

  .small-post .small-img,
  .arrow-main-snd {
    height: 200px;
  }

  .arrow,
  .arrow-snd {
    height: 30px;
    width: 30px;
  }
}

@media (max-width: 450px) {
  .blog-main {
    padding: 8px;
  }

  .img .img-img,
  .arrow-main {
    height: 200px;
  }

  .loader {
    margin: 1rem auto 8rem;
  }
}
