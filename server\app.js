const express = require("express");
const mongoose = require("mongoose");
const multer = require("multer");
const requestIp = require("request-ip");
require("dotenv").config();
const cookieParser = require("cookie-parser");
const punycode = require("punycode/");

const app = express();

// Use PORT and MONGO_URL from .env
const port = process.env.PORT || 3030;
const MONGO_URL = process.env.MONGO_URL;

app.use(express.json());
app.use(cookieParser());
app.use(requestIp.mw());

app.use((req, res, next) => {
  const allowedOrigins = process.env.ALLOW_ORIGINS || "http://localhost:3000";
  res.setHeader("Access-Control-Allow-Origin", allowedOrigins);
  res.setHeader("Access-Control-Allow-Methods", "GET, POST, DELETE, PATCH, PUT");
  res.setHeader("Access-Control-Allow-Credentials", "true");
  res.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
  next();
});

// Multer setup
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  fileFilter: (req, file, cb) => {
    const allowedTypes = ["image/jpeg", "image/png", "image/jpg"];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error("Invalid file type. Only JPEG, PNG, and JPG are allowed."));
    }
  },
});

app.use(upload.single("image"));

// Routes
const publicRoutes = require("./routes/public");
const authRoutes = require("./routes/auth");
const profileRoutes = require("./routes/profile");
const postRoutes = require("./routes/post");

app.use("/public", publicRoutes);
app.use("/auth", authRoutes);
app.use("/profile", profileRoutes);
app.use("/post", postRoutes);

// Error handling middleware
app.use((error, req, res, next) => {
  console.log(error);
  const status = error.statusCode || 500;
  const message = error.message || "Something went wrong";
  const data = error.data;
  res.status(status).json({ message, data, error: "yes", errors: error });
});

// Connect to MongoDB and start server
mongoose
  .connect(MONGO_URL)
  .then(() => {
    console.log("MongoDB connected successfully");  // success message for DB
    app.listen(port, () => {
      console.log(`Listening on port ${port}`);
    });
  })
  .catch((err) => {
    console.error("MongoDB connection error:", err);
  });
