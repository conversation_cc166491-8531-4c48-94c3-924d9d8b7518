@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;900&display=swap");
.div-main {
  display: flex;
  flex-flow: nowrap row;
  gap: 20px;
  width: 40%;
  margin: auto;
  align-items: center;
}

.cat-main {
  width: 90%;
  margin: 1rem auto;
  display: flex;
  gap: 15px;
  overflow: scroll;
  align-items: center;
  scroll-behavior: smooth;
  border: 0;
  border-radius: 25px;
}

.cat-main::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}

.cat-main::-webkit-scrollbar-thumb {
  background-color: transparent;
}

.cat-main::-webkit-scrollbar-track {
  background-color: transparent;
}

.category {
  flex: 1 1 50%;
  box-sizing: border-box;
  cursor: pointer;
  color: #3d3d3d;
}

.cat-btn button {
  border: 0px;
}

.cat-btn {
  border: 0;
  background: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
  font-weight: 400;
  color: black;
  cursor: pointer;
  font-size: 14px;
  font-family: "Inter", sans-serif;
}

.forward,
.back {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  background-color: #0cace5;
  box-shadow: 0px 0px 5px 2px rgba(9, 164, 226, 0.39);
}
.forward img,
.back img {
  width: 20px;
  height: 20px;
}

.active {
  background-color: #0cace5;

  padding: 5px 20px;
  border: 0;
  border-radius: 25px;
}

.active button {
  color: white !important;
}

@media (max-width: 1100px) {
  .div-main {
    width: 60%;
  }
}

@media (max-width: 730px) {
  .div-main {
    width: 70%;
  }
}

@media (max-width: 500px) {
  .div-main {
    width: 80%;
  }
}

@media (max-width: 500px) {
  .div-main {
    width: 90%;
  }
}

@media (max-width: 305px) {
  .div-main {
    width: 95%;
  }
  .cat-main {
    color: rgb(29, 29, 29);
  }

  .cat-main p {
    font-size: 0.9rem;
    font-weight: 400;
  }
  .active {
    padding: 5px 10px;
  }
}
