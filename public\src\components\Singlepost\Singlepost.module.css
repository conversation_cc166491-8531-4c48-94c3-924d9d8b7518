@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;900&display=swap");

.post-main {
  width: 100%;
  word-wrap: break-word;
  flex: 1;
  overflow: hidden;
}

.post-sub {
  width: 100%;
  background-color: #f6f8f7;
  padding: 2rem 0px;
  margin: auto;
  border: 0;
  border-radius: 12px;
}

.title-section {
  width: 1100px;
  margin: auto;
  display: flex;
  gap: 20px;
}

.title {
  flex: 1 1 55%;
  display: flex;
  flex-flow: nowrap column;
}

.image-section {
  width: 60%;
}

.image-section img {
  width: 100%;
  border-radius: 10px;
}

.image-section p {
  padding-top: 10px;
  color: rgb(65, 65, 65);
}

.title h1 {
  color: rgb(33, 30, 37);
  padding-bottom: 0.2rem;
  font-family: "Inter", sans-serif;
}

.desc {
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.6;
  margin-top: 10px;
  flex: 1 1 40%;
  color: #383838;
  padding-bottom: 0.3rem;
  font-family: "Inter", sans-serif;
}

.name {
  margin-top: 12px;
  font-weight: 400;
  font-size: 1rem;
}

.name span {
  color: rgb(0, 120, 150);
}

.date {
  padding-top: 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-flow: nowrap row;
}

.date p:nth-child(2) {
  font-weight: 600;
}

.views {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-flow: nowrap wrap;
  gap: 5px;
}

.text-section-main {
  width: 100%;
  background-color: #f6f8f7;
  padding-bottom: 8rem;
  border-top: 6px solid;
  border-top-color: rgba(255, 255, 255, 0.37);
}

.text-section {
  width: 100%;
  max-width: 1100px;
  margin: 0px auto;
  font-family: "Inter", sans-serif;
  box-sizing: border-box;
  padding-top: 1rem;
  line-height: 32px !important;
  border: 0px;
  border-radius: 12px;
  color: #181c25;
}

.text-section {
  padding-top: 1rem;
  font-size: 1.15rem;
  line-height: 1.6;
}

.loader {
  width: 100%;
  margin: auto;
  flex: 1;
  display: flex;
  justify-content: center;
  margin-top: 12rem;
}

p img {
  margin: auto;
  border: 0;
  border-radius: 12px;
  height: auto;
}

@media (max-width: 1100px) {
  .title-section,
  .text-section {
    width: 100%;
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

@media (max-width: 830px) {
  .title-section {
    flex-flow: nowrap column;
    gap: 20px;
  }
  .title h1 {
    font-size: 1.6rem;
  }

  .desc {
    font-size: 1.1rem;
  }
  .date {
    padding-top: 1rem;
  }
  .image-section img {
    height: 400px;
  }
  .image-section {
    width: 100%;
  }
}

@media (max-width: 730px) {
  .title h1 {
    font-size: 1.5rem;
  }
}

@media (max-width: 550px) {
  .image-section img {
    height: 300px;
  }
  p img {
    width: 100%;
    height: auto;
  }
  .post-main {
    margin-top: 10px;
  }
}
