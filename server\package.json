{"dependencies": {"bcryptjs": "^2.4.3", "cloudinary": "^1.41.1", "cookie-parser": "^1.4.6", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "punycode": "^2.3.1", "rate-limit-mongo": "^2.3.2", "request-ip": "^3.3.0"}, "name": "server", "version": "1.0.0", "main": "app.js", "devDependencies": {"nodemon": "^3.1.10"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node app.js", "dev": "nodemon app.js"}, "author": "", "license": "ISC", "description": ""}